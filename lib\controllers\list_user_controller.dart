import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../models/models.dart';
import '../repositories/i_backend_api.dart';
import '../router/router.dart';
import '../theme/app_colours.dart';
import '../utilities/snack_bar_show.dart';
import 'server_key_controller.dart';

class ListUserController extends ChangeNotifier {
  ListUserController() {
    getUsers();
  }

  List<BackendUser> users = [];
  bool isLoading = false;
  String? errorMessage;

  bool isLoadingUserInfo = false;
  UserInfoResponse? userInfo;

  Future<void> getUsers() async {
    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.getUsers(serverKey);

      users = response.users;

      debugPrint('Successfully loaded ${users.length} users');
    } catch (e) {
      errorMessage = e.toString();

      di<SnackBarShow>().showSnackBar(
        "Error : We couldn't load users",
        Colors.red,
      );
      debugPrint('Error loading users: $e');
      users = [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // Get detailed user info by ID
  Future<void> getUserInfo(String userId) async {
    userInfo = null;
    isLoadingUserInfo = true;
    notifyListeners();

    try {
      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.getUserInfo(serverKey, userId);

      debugPrint('Successfully loaded user info for: $userId');

      userInfo = response;
    } catch (e) {
      debugPrint('Error loading user info for $userId: $e');

      di<SnackBarShow>().showSnackBar(
        "Error : We couldn't load users info",
        AppColours.info,
      );
    } finally {
      isLoadingUserInfo = false;
      notifyListeners();
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.deleteUser(serverKey, userId);

      debugPrint('Successfully deleted user: $userId');

      di<SnackBarShow>().showSnackBar(
        "User deleted successfully",
        Colors.green,
      );

      await getUsers();

      await Future.delayed(const Duration(milliseconds: 300), () {
        router.go('/listUsersScreen');
      });
    } catch (e) {
      debugPrint('Error deleting user: $e');

      di<SnackBarShow>().showSnackBar(
        "Error : We couldn't delete user",
        Colors.red,
      );
    }
  }

  // Delete user
  Future<bool> deleteUser(String userId) async {
    try {
      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.deleteUser(serverKey, userId);

      // Remove user from local list
      users.removeWhere((user) => user.id == userId);
      notifyListeners();

      debugPrint('Successfully deleted user: ${response['message']}');
      return true;
    } catch (e) {
      debugPrint('Error deleting user $userId: $e');
      return false;
    }
  }

  // Helper getters
  List<BackendUser> get adminUsers =>
      users.where((user) => user.isAdmin).toList();
  List<BackendUser> get regularUsers =>
      users.where((user) => !user.isAdmin).toList();
  int get totalUsers => users.length;
  bool get hasUsers => users.isNotEmpty;
}
