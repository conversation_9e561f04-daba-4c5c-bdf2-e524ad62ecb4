import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../models/models.dart';
import '../repositories/i_backend_api.dart';
import 'server_key_controller.dart';

class ListUserController extends ChangeNotifier {
  ListUserController() {
    getUsers();
  }

  List<BackendUser> users = [];
  bool isLoading = false;
  String? errorMessage;

  Future<void> getUsers() async {
    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.getUsers(serverKey);

      // Update users list
      users = response.users;

      debugPrint('Successfully loaded ${users.length} users');
    } catch (e) {
      errorMessage = e.toString();
      debugPrint('Error loading users: $e');
      users = [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // Get detailed user info by ID
  Future<UserInfoResponse?> getUserInfo(String userId) async {
    try {
      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        throw Exception('No server key available');
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.getUserInfo(serverKey, userId);

      debugPrint('Successfully loaded user info for: $userId');
      return response;
    } catch (e) {
      debugPrint('Error loading user info for $userId: $e');
      return null;
    }
  }

  // Helper getters
  List<BackendUser> get adminUsers =>
      users.where((user) => user.isAdmin).toList();
  List<BackendUser> get regularUsers =>
      users.where((user) => !user.isAdmin).toList();
  int get totalUsers => users.length;
  bool get hasUsers => users.isNotEmpty;
}
