import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import 'server_key_controller.dart';

class ListUserController extends ChangeNotifier {
  ListUserController() {
    getUsers();
  }

  List<String> users = [];

  Future<void> getUsers() async {
    await di<ServerKeyController>().getServerKey();

    final serverKey = di<ServerKeyController>().serverKey;

    if (di<ServerKeyController>().hasServerKey) {
      debugPrint(serverKey);
    }

    notifyListeners();
  }
}
