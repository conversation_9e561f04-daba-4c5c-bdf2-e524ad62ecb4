import 'package:flutter/material.dart';

abstract class AppColours {
  // Primary Colors
  static const Color primaryBlue = Color(0xFF142F43);
  static const Color midBlue = Color(0xFF7BCBE3);
  static const Color lightBlue = Color.fromARGB(255, 206, 230, 249);

  // Secondary Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color lightGrey = Color(0xFFF5F5F5);
  static const Color darkGrey = Color(0xFF424242);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFF44336);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);

  // Background Colors
  static const Color backgroundPrimary = white;
  static const Color backgroundSecondary = lightGrey;
  static const Color backgroundDark = primaryBlue;

  // Text Colors
  static const Color textPrimary = black;
  static const Color textSecondary = darkGrey;
  static const Color textLight = white;
  static const Color textMuted = grey;

  // Button Colors
  static const Color buttonPrimary = primaryBlue;
  static const Color buttonSecondary = midBlue;
  static const Color buttonDisabled = grey;

  // Border Colors
  static const Color borderLight = lightGrey;
  static const Color borderMedium = grey;
  static const Color borderDark = darkGrey;

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  AppColours._();
}
