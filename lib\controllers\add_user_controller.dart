import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_backend_api.dart';
import '../router/router.dart';
import '../utilities/snack_bar_show.dart';
import 'server_key_controller.dart';

class AddUserController {
  Future<void> addUser(String email, String password, String name) async {
    try {
      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        di<SnackBarShow>().showSnackBar("No server key available", Colors.red);
        return;
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.addUser(
        serverKey,
        email,
        password,
        name,
      );

      debugPrint(response.toString());

      debugPrint('Successfully added user: $email');

      di<SnackBarShow>().showSnackBar("User added successfully", Colors.green);

      await Future.delayed(const Duration(milliseconds: 300), () {
        router.go('/listUsersScreen');
      });

      //
      //
      //
    } catch (e) {
      debugPrint('Error adding user: $e');

      di<SnackBarShow>().showSnackBar(
        "Error : We couldn't add user",
        Colors.red,
      );
    }
  }
}
