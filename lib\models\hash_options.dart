class HashOptions {
  final int memoryCost;
  final int threads;
  final int timeCost;
  final String type;

  HashOptions({
    required this.memoryCost,
    required this.threads,
    required this.timeCost,
    required this.type,
  });

  factory HashOptions.fromJson(Map<String, dynamic> json) {
    return HashOptions(
      memoryCost: json['memoryCost'] ?? 0,
      threads: json['threads'] ?? 0,
      timeCost: json['timeCost'] ?? 0,
      type: json['type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'memoryCost': memoryCost,
      'threads': threads,
      'timeCost': timeCost,
      'type': type,
    };
  }
}
