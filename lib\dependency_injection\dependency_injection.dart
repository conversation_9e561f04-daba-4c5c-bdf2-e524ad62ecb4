import 'package:appwrite/appwrite.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/auth_controller.dart';
import '../controllers/list_user_controller.dart';
import '../controllers/server_key_controller.dart';
import '../repositories/auth_repository.dart';
import '../repositories/i_auth_repository.dart';
import '../server_config/server_config.dart';
import '../utilities/snack_bar_show.dart';

initializeDependencies() {
  final client = Client()
    ..setEndpoint(ServerConfig.appWriteEndpoint)
    ..setProject(ServerConfig.appWriteProjectId);

  final account = Account(client);

  di.registerSingleton<IAuthRepository>(AuthRepository(account: account));

  di.registerSingleton<AuthController>(
    AuthController(repository: di<IAuthRepository>()),
  );

  di.registerSingleton<SnackBarShow>(SnackBarShow());

  di.registerLazySingleton<ServerKeyController>(
    () => ServerKeyController(repository: di<IAuthRepository>()),
  );

  di.registerLazySingleton<ListUserController>(() => ListUserController());
}
