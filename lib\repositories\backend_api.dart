import 'package:dio/dio.dart';
import '../models/models.dart';
import '../exceptions/repository_exception.dart';
import 'i_backend_api.dart';

class BackendApi implements IBackendApi {
  static const String baseUrl = 'https://surgassistsbackendar.online';
  final Dio _dio = Dio();

  @override
  Future<UsersResponse> getUsers(String serverKey) async {
    try {
      final response = await _dio.get(
        '$baseUrl/admin-get-list-users',
        options: Options(
          headers: {
            'Authorization': 'Bearer $serverKey',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return UsersResponse.fromJson(response.data);
      } else {
        throw RepositoryException(
          'Failed to load users: ${response.statusCode} - ${response.data}',
        );
      }
    } on DioException catch (e) {
      throw RepositoryException('Failed to load users: ${e.message}');
    } catch (e) {
      throw RepositoryException('Failed to load users: $e');
    }
  }

  @override
  Future<UserInfoResponse> getUserInfo(String serverKey, String userId) async {
    try {
      final response = await _dio.get(
        '$baseUrl/admin-get-user-info/$userId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $serverKey',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return UserInfoResponse.fromJson(response.data);
      } else {
        throw RepositoryException(
          'Failed to load user info: ${response.statusCode} - ${response.data}',
        );
      }
    } on DioException catch (e) {
      throw RepositoryException('Failed to load user info: ${e.message}');
    } catch (e) {
      throw RepositoryException('Failed to load user info: $e');
    }
  }

  @override
  Future addUser(
    String serverKey,
    String email,
    String password,
    String name,
  ) async {
    try {
      final response = await _dio.post(
        '$baseUrl/admin-create-user',
        options: Options(
          headers: {
            'Authorization': 'Bearer $serverKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {'email': email, 'password': password, 'name': name},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return;
      } else {
        throw RepositoryException(
          'Failed to add user: ${response.statusCode} - ${response.data}',
        );
      }
    } on DioException catch (e) {
      throw RepositoryException('Failed to add user: ${e.message}');
    } catch (e) {
      throw RepositoryException('Failed to add user: $e');
    }
  }


  Future<

}
