import 'i_backend_api.dart';

class BackendApi implements IBackendApi {

  


  @override
  Future<void> getUsers(String serverKey) async {
    try {
      final response = await http.get(
        Uri.parse('https://surgassistsbackendar.online/api/users'),
        headers: {'Authorization': 'Bearer $serverKey'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.cast<String>();
      } else {
        throw Exception('Failed to load users');
      }
    } catch (e) {
      throw Exception('Failed to load users: $e');
    }
  }     

  

}
