import 'dart:convert';

import '../models/models.dart';
import '../exceptions/repository_exception.dart';
import 'i_backend_api.dart';

class BackendApi implements IBackendApi {
  static const String baseUrl = 'https://surgassistsbackendar.online';

  @override
  Future<UsersResponse> getUsers(String serverKey) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/admin-get-list-users'),
        headers: {
          'Authorization': 'Bearer $serverKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return UsersResponse.fromJson(jsonData);
      } else {
        throw RepositoryException(
          'Failed to load users: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (e is RepositoryException) {
        rethrow;
      }
      throw RepositoryException('Failed to load users: $e');
    }
  }
}
