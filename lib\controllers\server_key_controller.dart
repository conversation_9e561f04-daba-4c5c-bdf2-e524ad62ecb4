import 'package:flutter/material.dart';
import '../repositories/i_auth_repository.dart';

class Server<PERSON>eyController {
  final IAuthRepository _repository;

  ServerKeyController({required IAuthRepository repository})
    : _repository = repository;

  String serverKey = "";

  Future<void> getServerKey() async {
    try {
      // Get user preferences from Appwrite
      final prefs = await _repository.getUserPreferences();

      // Extract the server key from preferences
      serverKey = prefs['key'] ?? "";

      debugPrint(
        'Server key retrieved: ${serverKey.isNotEmpty ? "Found" : "Not found"}',
      );
    } catch (e) {
      debugPrint('Error getting server key: $e');
      serverKey = "";
    }
  }

  bool get hasServerKey => serverKey.isNotEmpty;
}
