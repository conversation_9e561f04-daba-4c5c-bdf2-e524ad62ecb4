import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../theme/app_colours.dart';

class StartImageScreen extends StatelessWidget {
  const StartImageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
            child: Image.asset(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              'assets/images/load-screen.png',
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 30,
            right: 30,
            child: ElevatedButton(
              onPressed: () {
                context.go('/loginScreen');
              },
              child: Text(
                'Login',
                style: TextStyle(color: AppColours.primaryBlue, fontSize: 30),
              ),
            ),
          ),
        ],
      ),
    );
  }
}