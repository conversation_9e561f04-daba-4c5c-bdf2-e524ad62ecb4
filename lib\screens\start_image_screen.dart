import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';
import '../controllers/auth_controller.dart';
import '../repositories/i_auth_repository.dart';
import '../theme/theme.dart';

class StartImageScreen extends StatefulWidget {
  const StartImageScreen({super.key});

  @override
  State<StartImageScreen> createState() => _StartImageScreenState();
}

class _StartImageScreenState extends State<StartImageScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthenticationStatus();
  }

  Future<void> _checkAuthenticationStatus() async {
    final authController = di<AuthController>();
    final isLoggedIn = await authController.isUserLoggedIn();

    if (isLoggedIn && mounted) {
      try {
        // Get current user to check admin status
        final user = await di<IAuthRepository>().getCurrentUser();
        final userLabels = user.labels;
        final bool isAdmin = userLabels.contains('admin');

        if (isAdmin) {
          // User is admin, navigate to main screen
          if (mounted) {
            context.go('/mainScreen');
          }
        } else {
          // User is not admin, logout and stay on this screen
          await authController.logout();
        }
      } catch (e) {
        // Error getting user info, logout to be safe
        await authController.logout();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
            child: Image.asset(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              'assets/images/load-screen.png',
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 30,
            right: 30,
            child: ElevatedButton(
              onPressed: () {
                context.go('/loginScreen');
              },
              child: Text(
                'Login',
                style: AppTypography.h3.copyWith(color: AppColours.primaryBlue),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
