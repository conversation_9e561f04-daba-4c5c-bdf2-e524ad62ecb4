import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';
import '../controllers/auth_controller.dart';
import '../theme/app_colours.dart';

class StartImageScreen extends StatefulWidget {
  const StartImageScreen({super.key});

  @override
  State<StartImageScreen> createState() => _StartImageScreenState();
}

class _StartImageScreenState extends State<StartImageScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
            child: Image.asset(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              'assets/images/load-screen.png',
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 30,
            right: 30,
            child: ElevatedButton(
              onPressed: () {
                context.go('/loginScreen');
              },
              child: Text(
                'Login',
                style: TextStyle(color: AppColours.primaryBlue, fontSize: 30),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
