abstract class AppSpacing {
  // Base spacing unit (8px)
  static const double base = 8.0;

  // Spacing Scale
  static const double xs = base * 0.5; // 4px
  static const double sm = base; // 8px
  static const double md = base * 2; // 16px
  static const double lg = base * 3; // 24px
  static const double xl = base * 4; // 32px
  static const double xxl = base * 6; // 48px
  static const double xxxl = base * 8; // 64px

  // Common Padding Values
  static const double paddingXS = xs;
  static const double paddingSM = sm;
  static const double paddingMD = md;
  static const double paddingLG = lg;
  static const double paddingXL = xl;

  // Common Margin Values
  static const double marginXS = xs;
  static const double marginSM = sm;
  static const double marginMD = md;
  static const double marginLG = lg;
  static const double marginXL = xl;

  // Screen Padding (for consistent screen margins)
  static const double screenPaddingHorizontal = md;
  static const double screenPaddingVertical = lg;

  // Component Spacing
  static const double buttonPadding = md;
  static const double cardPadding = md;
  static const double listItemPadding = sm;
  static const double sectionSpacing = xl;

  // Border Radius
  static const double radiusXS = 2.0;
  static const double radiusSM = 4.0;
  static const double radiusMD = 8.0;
  static const double radiusLG = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusRound = 50.0;

  // Icon Sizes
  static const double iconXS = 12.0;
  static const double iconSM = 16.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 48.0;

  // Button Heights
  static const double buttonHeightSM = 32.0;
  static const double buttonHeightMD = 40.0;
  static const double buttonHeightLG = 48.0;

  // Input Field Heights
  static const double inputHeightSM = 36.0;
  static const double inputHeightMD = 44.0;
  static const double inputHeightLG = 52.0;

  AppSpacing._();
}
