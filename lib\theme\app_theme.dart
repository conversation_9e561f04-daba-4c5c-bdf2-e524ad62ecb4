import 'package:flutter/material.dart';
import 'app_colours.dart';
import 'app_typography.dart';
import 'app_spacing.dart';

abstract class AppTheme {
  // Main Theme Data
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColours.primaryBlue,
        brightness: Brightness.light,
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColours.lightBlue,
        foregroundColor: AppColours.textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: AppTypography.h5.copyWith(
          color: AppColours.primaryBlue,
        ),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColours.buttonPrimary,
          foregroundColor: AppColours.textLight,
          textStyle: AppTypography.buttonMedium,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.paddingLG,
            vertical: AppSpacing.paddingMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          ),
          minimumSize: const Size(0, AppSpacing.buttonHeightMD),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColours.buttonPrimary,
          textStyle: AppTypography.buttonMedium,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.paddingMD,
            vertical: AppSpacing.paddingSM,
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColours.buttonPrimary,
          textStyle: AppTypography.buttonMedium,
          side: const BorderSide(color: AppColours.buttonPrimary),
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.paddingLG,
            vertical: AppSpacing.paddingMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          ),
          minimumSize: const Size(0, AppSpacing.buttonHeightMD),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColours.backgroundSecondary,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          borderSide: const BorderSide(color: AppColours.borderLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          borderSide: const BorderSide(color: AppColours.borderLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          borderSide: const BorderSide(
            color: AppColours.buttonPrimary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
          borderSide: const BorderSide(color: AppColours.error),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.paddingMD,
          vertical: AppSpacing.paddingMD,
        ),
        labelStyle: AppTypography.label,
        hintStyle: AppTypography.bodyMedium.copyWith(
          color: AppColours.textMuted,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: AppColours.backgroundPrimary,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLG),
        ),
        margin: const EdgeInsets.all(AppSpacing.marginSM),
      ),

      // Scaffold Background
      scaffoldBackgroundColor: AppColours.backgroundPrimary,

      // Use default text theme to avoid inheritance issues
      fontFamily: AppTypography.fontFamily,
    );
  }

  // Dark Theme (optional)
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      brightness: Brightness.dark,
      scaffoldBackgroundColor: AppColours.backgroundDark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColours.primaryBlue,
        brightness: Brightness.dark,
      ),
    );
  }

  AppTheme._();
}
