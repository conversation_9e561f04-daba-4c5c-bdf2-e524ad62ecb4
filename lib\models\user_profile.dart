class UserProfile {
  final String id;
  final String userId;
  final String createdAt;
  final String updatedAt;
  final List<String> permissions;
  final String collectionId;
  final String databaseId;
  final String sequence;
  final String address1;
  final String address2;
  final String consultantName;
  final String postCode;
  final String telephone;
  final Map<String, dynamic> additionalAttributes;

  UserProfile({
    required this.id,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    required this.permissions,
    required this.collectionId,
    required this.databaseId,
    required this.sequence,
    required this.address1,
    required this.address2,
    required this.consultantName,
    required this.postCode,
    required this.telephone,
    required this.additionalAttributes,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    // Extract the known fields
    final knownFields = {
      '\$id',
      'UserID',
      '\$createdAt',
      '\$updatedAt',
      '\$permissions',
      '\$collectionId',
      '\$databaseId',
      '\$sequence',
      'Address1',
      'Address2',
      'ConsultantName',
      'PostCode',
      'Telephone',
    };

    // Get additional attributes (any fields not in the known fields)
    final additionalAttributes = <String, dynamic>{};
    json.forEach((key, value) {
      if (!knownFields.contains(key)) {
        additionalAttributes[key] = value;
      }
    });

    return UserProfile(
      id: json['\$id'] ?? '',
      userId: json['UserID'] ?? '',
      createdAt: json['\$createdAt'] ?? '',
      updatedAt: json['\$updatedAt'] ?? '',
      permissions: List<String>.from(json['\$permissions'] ?? []),
      collectionId: json['\$collectionId'] ?? '',
      databaseId: json['\$databaseId'] ?? '',
      sequence: json['\$sequence'] ?? '',
      address1: json['Address1'] ?? '',
      address2: json['Address2'] ?? '',
      consultantName: json['ConsultantName'] ?? '',
      postCode: json['PostCode'] ?? '',
      telephone: json['Telephone'] ?? '',
      additionalAttributes: additionalAttributes,
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      '\$id': id,
      'UserID': userId,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      '\$permissions': permissions,
      '\$collectionId': collectionId,
      '\$databaseId': databaseId,
      '\$sequence': sequence,
      'Address1': address1,
      'Address2': address2,
      'ConsultantName': consultantName,
      'PostCode': postCode,
      'Telephone': telephone,
    };

    // Add additional attributes
    json.addAll(additionalAttributes);

    return json;
  }

  // Helper method to get a specific attribute
  T? getAttribute<T>(String key) {
    return additionalAttributes[key] as T?;
  }

  // Helper method to check if an attribute exists
  bool hasAttribute(String key) {
    return additionalAttributes.containsKey(key);
  }
}

class UserInfoResponse {
  final String message;
  final String userId;
  final UserProfile profile;

  UserInfoResponse({
    required this.message,
    required this.userId,
    required this.profile,
  });

  factory UserInfoResponse.fromJson(Map<String, dynamic> json) {
    return UserInfoResponse(
      message: json['message'] ?? '',
      userId: json['user_id'] ?? '',
      profile: UserProfile.fromJson(json['profile'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'message': message, 'user_id': userId, 'profile': profile.toJson()};
  }
}
