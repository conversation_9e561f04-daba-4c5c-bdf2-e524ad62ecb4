import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/auth_controller.dart';

import '../theme/app_colours.dart';

class SettingScreen extends StatelessWidget {
  const SettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: const Text('Settings'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Logout Section
              Card(
                color: AppColours.lightBlue,
                elevation: 0,
                child: ListTile(
                  title: const Text('Logout', style: TextStyle(fontSize: 20)),
                  trailing: const Icon(Icons.logout),
                  onTap: () {
                    di<AuthController>().logout();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
