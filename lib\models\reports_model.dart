class ReportDocument {
  final String id;
  final String collectionId;
  final String databaseId;
  final String createdAt;
  final String updatedAt;
  final List<String> permissions;
  final String sequence;
  final String appName;
  final String qrCode;

  ReportDocument({
    required this.id,
    required this.collectionId,
    required this.databaseId,
    required this.createdAt,
    required this.updatedAt,
    required this.permissions,
    required this.sequence,
    required this.appName,
    required this.qrCode,
  });

  factory ReportDocument.fromJson(Map<String, dynamic> json) {
    return ReportDocument(
      id: json['\$id'] ?? '',
      collectionId: json['\$collectionId'] ?? '',
      databaseId: json['\$databaseId'] ?? '',
      createdAt: json['\$createdAt'] ?? '',
      updatedAt: json['\$updatedAt'] ?? '',
      permissions: List<String>.from(json['\$permissions'] ?? []),
      sequence: json['\$sequence'] ?? '',
      appName: json['AppName'] ?? '',
      qrCode: json['QRCode'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$collectionId': collectionId,
      '\$databaseId': databaseId,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      '\$permissions': permissions,
      '\$sequence': sequence,
      'AppName': appName,
      'QRCode': qrCode,
    };
  }

  // Helper getters
  DateTime? get createdAtDateTime {
    try {
      return DateTime.parse(createdAt);
    } catch (e) {
      return null;
    }
  }

  DateTime? get updatedAtDateTime {
    try {
      return DateTime.parse(updatedAt);
    } catch (e) {
      return null;
    }
  }

  String get formattedCreatedAt {
    final dateTime = createdAtDateTime;
    if (dateTime == null) return 'N/A';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String get formattedUpdatedAt {
    final dateTime = updatedAtDateTime;
    if (dateTime == null) return 'N/A';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class ReportsData {
  final List<ReportDocument> documents;
  final int total;

  ReportsData({required this.documents, required this.total});

  factory ReportsData.fromJson(Map<String, dynamic> json) {
    return ReportsData(
      documents:
          (json['documents'] as List<dynamic>?)
              ?.map((doc) => ReportDocument.fromJson(doc))
              .toList() ??
          [],
      total: json['total'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documents': documents.map((doc) => doc.toJson()).toList(),
      'total': total,
    };
  }

  // Helper getters
  bool get isEmpty => documents.isEmpty;
  bool get isNotEmpty => documents.isNotEmpty;
  int get count => documents.length;

  // Get documents by app name
  List<ReportDocument> getDocumentsByAppName(String appName) {
    return documents.where((doc) => doc.appName == appName).toList();
  }

  // Get unique app names
  List<String> get uniqueAppNames {
    return documents.map((doc) => doc.appName).toSet().toList();
  }

  // Get documents by QR code
  List<ReportDocument> getDocumentsByQRCode(String qrCode) {
    return documents.where((doc) => doc.qrCode == qrCode).toList();
  }
}

class ReportsResponse {
  final ReportsData data;
  final String message;

  ReportsResponse({required this.data, required this.message});

  factory ReportsResponse.fromJson(Map<String, dynamic> json) {
    return ReportsResponse(
      data: ReportsData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'data': data.toJson(), 'message': message};
  }

  // Helper getters
  List<ReportDocument> get documents => data.documents;
  int get totalReports => data.total;
  bool get hasReports => data.isNotEmpty;
  List<String> get appNames => data.uniqueAppNames;
}
