class UserTarget {
  final String id;
  final String createdAt;
  final String updatedAt;
  final bool expired;
  final String identifier;
  final String name;
  final String? providerId;
  final String providerType;
  final String userId;

  UserTarget({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.expired,
    required this.identifier,
    required this.name,
    this.providerId,
    required this.providerType,
    required this.userId,
  });

  factory UserTarget.fromJson(Map<String, dynamic> json) {
    return UserTarget(
      id: json['\$id'] ?? '',
      createdAt: json['\$createdAt'] ?? '',
      updatedAt: json['\$updatedAt'] ?? '',
      expired: json['expired'] ?? false,
      identifier: json['identifier'] ?? '',
      name: json['name'] ?? '',
      providerId: json['providerId'],
      providerType: json['providerType'] ?? '',
      userId: json['userId'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      'expired': expired,
      'identifier': identifier,
      'name': name,
      'providerId': providerId,
      'providerType': providerType,
      'userId': userId,
    };
  }
}
