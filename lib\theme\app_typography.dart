import 'package:flutter/material.dart';
import 'app_colours.dart';

abstract class AppTypography {
  // Font Family
  static const String fontFamily = 'Roboto'; // Change this to your preferred font
  
  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  
  // Heading Styles
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: bold,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle h2 = TextStyle(
    fontSize: 28,
    fontWeight: bold,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle h3 = TextStyle(
    fontSize: 24,
    fontWeight: semiBold,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle h4 = TextStyle(
    fontSize: 20,
    fontWeight: semiBold,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle h5 = TextStyle(
    fontSize: 18,
    fontWeight: medium,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle h6 = TextStyle(
    fontSize: 16,
    fontWeight: medium,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  // Body Text Styles
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    color: AppColours.textPrimary,
    fontFamily: fontFamily,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.textSecondary,
    fontFamily: fontFamily,
  );
  
  // Button Text Styles
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: semiBold,
    color: AppColours.textLight,
    fontFamily: fontFamily,
  );
  
  static const TextStyle buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: semiBold,
    color: AppColours.textLight,
    fontFamily: fontFamily,
  );
  
  static const TextStyle buttonSmall = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    color: AppColours.textLight,
    fontFamily: fontFamily,
  );
  
  // Caption and Label Styles
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.textMuted,
    fontFamily: fontFamily,
  );
  
  static const TextStyle label = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    color: AppColours.textSecondary,
    fontFamily: fontFamily,
  );
  
  // Special Styles
  static const TextStyle error = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.error,
    fontFamily: fontFamily,
  );
  
  static const TextStyle success = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.success,
    fontFamily: fontFamily,
  );

  AppTypography._();
}
