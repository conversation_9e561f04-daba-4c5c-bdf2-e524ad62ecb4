import 'package:flutter/material.dart';
import 'app_colours.dart';

abstract class AppTypography {
  // Font Family
  static const String fontFamily =
      'Roboto'; // Change this to your preferred font

  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Heading Styles
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: bold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle h2 = TextStyle(
    fontSize: 28,
    fontWeight: bold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle h3 = TextStyle(
    fontSize: 24,
    fontWeight: semiBold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle h4 = TextStyle(
    fontSize: 20,
    fontWeight: semiBold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle h5 = TextStyle(
    fontSize: 18,
    fontWeight: medium,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle h6 = TextStyle(
    fontSize: 16,
    fontWeight: medium,
    fontFamily: fontFamily,
    inherit: true,
  );

  // Body Text Styles
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    fontFamily: fontFamily,
    inherit: true,
  );

  // Button Text Styles
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: semiBold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: semiBold,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    fontFamily: fontFamily,
    inherit: true,
  );

  // Caption and Label Styles
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle label = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    fontFamily: fontFamily,
    inherit: true,
  );

  // Special Styles
  static const TextStyle error = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.error,
    fontFamily: fontFamily,
    inherit: true,
  );

  static const TextStyle success = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    color: AppColours.success,
    fontFamily: fontFamily,
    inherit: true,
  );

  AppTypography._();
}
