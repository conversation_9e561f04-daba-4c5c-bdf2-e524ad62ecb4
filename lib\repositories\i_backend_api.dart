import '../models/models.dart';

abstract class IBackendApi {
  Future<UsersResponse> getUsers(String serverKey);
  Future<UserInfoResponse> getUserInfo(String serverKey, String userId);
  Future addUser(
    String serverKey,
    String email,
    String password,
    String name,
  ) async {}
  Future<ReportsResponse> getReports(String serverKey);

  Future deleteUser(String serverKey, String userId) async {}
}
