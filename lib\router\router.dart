import 'package:go_router/go_router.dart';

import '../screens/add_user_screen.dart';
import '../screens/list_users_screen.dart';
import '../screens/login_screen.dart';
import '../screens/main_screen.dart';
import '../screens/reports_screen.dart';
import '../screens/setting_screen.dart';
import '../screens/start_image_screen.dart';
import '../screens/user_details_screen.dart';

final GoRouter router = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: '/',
  routes: [
    GoRoute(path: '/', builder: (context, state) => StartImageScreen()),
    GoRoute(path: '/loginScreen', builder: (context, state) => LoginScreen()),
    GoRoute(path: '/mainScreen', builder: (context, state) => MainScreen()),
    GoRoute(
      path: '/settingScreen',
      builder: (context, state) => SettingScreen(),
    ),
    GoRoute(
      path: '/listUsersScreen',
      builder: (context, state) => ListUsersScreen(),
    ),
    GoRoute(
      path: '/addUserScreen',
      builder: (context, state) => AddUserScreen(),
    ),
    GoRoute(
      path: '/userDetailsScreen/:id',
      builder: (context, state) {
        final userId = state.pathParameters['id'] ?? '';
        return UserDetailsScreen(userId: userId);
      },
    ),
    GoRoute(
      path: '/reportsScreen',
      builder: (context, state) => ReportsScreen(),
    ),
  ],
);
