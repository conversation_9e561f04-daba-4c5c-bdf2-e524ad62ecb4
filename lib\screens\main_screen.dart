import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../theme/app_colours.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('Main Screen'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings, size: 30),
            onPressed: () {
              context.go('/settingScreen');
            },
          ),
        ],
      ),
      body: Center(child: Text('Main Screen')),
    );
  }
}
