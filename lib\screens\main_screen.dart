import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../theme/theme.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Surgassists AR', style: AppTypography.h5),
        actions: [
          IconButton(
            icon: Icon(Icons.settings, size: AppSpacing.iconLG),
            onPressed: () {
              context.push('/settingScreen');
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppSpacing.screenPaddingHorizontal),
        children: [
          SizedBox(height: AppSpacing.sectionSpacing),
          Card(
            color: AppColours.lightBlue,
            child: ListTile(
              title: Text('List Users', style: AppTypography.h5),
              trailing: Icon(Icons.people, size: AppSpacing.iconLG),
              onTap: () {
                context.push('/listUsersScreen');
              },
            ),
          ),
          SizedBox(height: AppSpacing.marginMD),
          Card(
            color: AppColours.lightBlue,
            child: ListTile(
              title: Text('Add User', style: AppTypography.h5),
              trailing: Icon(Icons.person_add, size: AppSpacing.iconLG),
              onTap: () {
                context.push('/addUserScreen');
              },
            ),
          ),
          SizedBox(height: AppSpacing.marginMD),
          Card(
            color: AppColours.lightBlue,
            child: ListTile(
              title: Text('Reports', style: AppTypography.h5),
              trailing: Icon(Icons.bar_chart, size: AppSpacing.iconLG),
              onTap: () {
                context.push('/reportsScreen');
              },
            ),
          ),
        ],
      ),
    );
  }
}
