import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../theme/app_colours.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('Surgassists AR'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.settings, size: 30),
            onPressed: () {
              context.push('/settingScreen');
            },
          ),
        ],
      ),
      body: ListView(
        padding: EdgeInsets.only(left: 16, right: 16, top: 8),
        children: [
          SizedBox(height: 20),
          Card(
            color: AppColours.lightBlue,
            elevation: 0,
            child: ListTile(
              title: const Text('List Users', style: TextStyle(fontSize: 20)),
              trailing: const Icon(Icons.people, size: 30),
              onTap: () {
                context.push('/listUsersScreen');
              },
            ),
          ),
          SizedBox(height: 10),
          Card(
            color: AppColours.lightBlue,
            elevation: 0,
            child: ListTile(
              title: const Text('Add User', style: TextStyle(fontSize: 20)),
              trailing: const Icon(Icons.person_add, size: 30),
              onTap: () {
                context.push('/addUserScreen');
              },
            ),
          ),
        ],
      ),
    );
  }
}
