import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_auth_repository.dart';
import '../router/router.dart';
import '../utilities/snack_bar_show.dart';

class AuthController {
  final IAuthRepository _repository;

  AuthController({required IAuthRepository repository})
    : _repository = repository;

  Future<void> login(String email, String password) async {
    try {
      await _repository.login(email, password);
    } catch (e) {
      debugPrint(e.toString());
      di<SnackBarShow>().showSnackBar("Error : We couldn't login", Colors.red);
      return;
    }

    try {
      final user = await di<IAuthRepository>().getCurrentUser();
      final userLabels = user.labels;
      final bool isAdmin = userLabels.contains('admin');
      debugPrint('User is admin: $isAdmin');

      if (isAdmin) {
        router.go('/mainScreen');
      } else {
        // User is not an admin, log them out and redirect
        di<SnackBarShow>().showSnackBar(
          "Access denied: Admin privileges required",
          Colors.red,
        );
        await logout();

        await Future.delayed(const Duration(milliseconds: 600));

        router.go('/');
      }
    } catch (_) {
      di<SnackBarShow>().showSnackBar("Authentication error", Colors.red);
      await logout();
      router.go('/');
    }
  }

  Future<void> logout() async {
    try {
      await _repository.logout();
      router.go('/');
    } catch (_) {
      di<SnackBarShow>().showSnackBar("Error : We couldn't logout", Colors.red);
    }
  }

  Future<bool> isUserLoggedIn() async {
    return await _repository.isUserLoggedIn();
  }
}
