import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/reports_screen_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';
import '../theme/app_spacing.dart';
import '../theme/app_typography.dart';

class ReportsScreen extends StatelessWidget with WatchItMixin {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ReportsScreenController>();

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: const Text('Reports'),
        centerTitle: true,
      ),
      body: controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : controller.reports.isEmpty
          ? const Center(
              child: Text('No reports available', style: AppTypography.h5),
            )
          : ListView(
              padding: const EdgeInsets.only(
                top: AppSpacing.paddingMD,
                left: AppSpacing.paddingMD,
                right: AppSpacing.paddingMD,
                bottom: 120,
              ),
              children: [
                // Summary Cards
                _buildSummaryCards(controller.reports),

                const SizedBox(height: AppSpacing.sectionSpacing),

                // Reports by App Name Chart
                _buildAppNameChart(controller.reports),

                const SizedBox(height: AppSpacing.sectionSpacing),

                // Reports Over Time Chart
                _buildTimeChart(controller.reports),

                const SizedBox(height: AppSpacing.sectionSpacing),

                // Reports List
                _buildReportsList(controller.reports),
              ],
            ),
    );
  }

  Widget _buildSummaryCards(List<ReportDocument> reports) {
    final appNames = reports.map((r) => r.appName).toSet().toList();
    final qrCodes = reports.map((r) => r.qrCode).toSet().toList();

    return Row(
      children: [
        Expanded(
          child: Card(
            color: AppColours.lightBlue,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.paddingMD),
              child: Column(
                children: [
                  Text(
                    '${reports.length}',
                    style: AppTypography.h2.copyWith(
                      color: AppColours.primaryBlue,
                    ),
                  ),
                  const Text('Reports', style: AppTypography.bodyMedium),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.marginMD),
        Expanded(
          child: Card(
            color: AppColours.lightBlue,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.paddingMD),
              child: Column(
                children: [
                  Text(
                    '${appNames.length}',
                    style: AppTypography.h2.copyWith(
                      color: AppColours.primaryBlue,
                    ),
                  ),
                  const Text('App Types', style: AppTypography.bodyMedium),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.marginMD),
        Expanded(
          child: Card(
            color: AppColours.lightBlue,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.paddingMD),
              child: Column(
                children: [
                  Text(
                    '${qrCodes.length}',
                    style: AppTypography.h2.copyWith(
                      color: AppColours.primaryBlue,
                    ),
                  ),
                  const Text('QR Codes', style: AppTypography.bodyMedium),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppNameChart(List<ReportDocument> reports) {
    final appNameCounts = <String, int>{};
    for (final report in reports) {
      appNameCounts[report.appName] = (appNameCounts[report.appName] ?? 0) + 1;
    }

    final colors = [
      AppColours.primaryBlue,
      AppColours.midBlue,
      AppColours.success,
      AppColours.warning,
      AppColours.error,
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.paddingMD),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Reports by App Name', style: AppTypography.h5),
            const SizedBox(height: AppSpacing.marginMD),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: appNameCounts.entries.map((entry) {
                    final index = appNameCounts.keys.toList().indexOf(
                      entry.key,
                    );
                    final color = colors[index % colors.length];
                    final percentage = (entry.value / reports.length * 100)
                        .toStringAsFixed(1);

                    return PieChartSectionData(
                      color: color,
                      value: entry.value.toDouble(),
                      title: '$percentage%',
                      radius: 80,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColours.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.marginMD),
            // Legend
            Wrap(
              spacing: AppSpacing.marginMD,
              runSpacing: AppSpacing.marginSM,
              children: appNameCounts.entries.map((entry) {
                final index = appNameCounts.keys.toList().indexOf(entry.key);
                final color = colors[index % colors.length];

                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.marginSM),
                    Text(
                      '${entry.key} (${entry.value})',
                      style: AppTypography.bodySmall,
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeChart(List<ReportDocument> reports) {
    // Group reports by date
    final dateCounts = <DateTime, int>{};
    for (final report in reports) {
      final date = report.createdAtDateTime;
      if (date != null) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        dateCounts[dateOnly] = (dateCounts[dateOnly] ?? 0) + 1;
      }
    }

    if (dateCounts.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.paddingMD),
          child: Text('No date information available'),
        ),
      );
    }

    final sortedDates = dateCounts.keys.toList()..sort();
    final spots = <FlSpot>[];

    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final count = dateCounts[date]!;
      spots.add(FlSpot(i.toDouble(), count.toDouble()));
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.paddingMD),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Reports Over Time', style: AppTypography.h5),
            const SizedBox(height: AppSpacing.marginMD),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < sortedDates.length) {
                            final date = sortedDates[index];
                            return Text(
                              '${date.day}/${date.month}',
                              style: AppTypography.bodySmall,
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      color: AppColours.primaryBlue,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColours.primaryBlue.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsList(List<ReportDocument> reports) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.paddingMD),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Recent Reports', style: AppTypography.h5),
            const SizedBox(height: AppSpacing.marginMD),
            ...reports
                .take(10)
                .map(
                  (report) => ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColours.midBlue,
                      child: Text(
                        report.sequence,
                        style: const TextStyle(
                          color: AppColours.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(report.appName, style: AppTypography.h5),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Created: ${report.formattedCreatedAt}',
                          style: AppTypography.bodyMedium,
                        ),
                        Text(
                          'QR: ${report.qrCode}',
                          style: AppTypography.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
            if (reports.length > 10)
              Padding(
                padding: const EdgeInsets.only(top: AppSpacing.marginMD),
                child: Center(
                  child: Text(
                    'Showing 10 of ${reports.length} reports',
                    style: AppTypography.caption,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
