import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/reports_screen_controller.dart';
import '../theme/app_colours.dart';
import '../theme/app_typography.dart';

class ReportsScreen extends StatelessWidget with WatchItMixin {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ReportsScreenController>();

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: const Text('Reports'),
        centerTitle: true,
      ),
      body: controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: controller.reports
                  .map(
                    (report) => Card(
                      child: ListTile(
                        title: Text(report.appName, style: AppTypography.h4),
                        subtitle: Text(report.qrCode, style: AppTypography.h6),
                      ),
                    ),
                  )
                  .toList(),
            ),
    );
  }
}
