import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../models/reports_model.dart';
import '../repositories/i_backend_api.dart';
import '../utilities/snack_bar_show.dart';
import 'server_key_controller.dart';

class ReportsScreenController extends ChangeNotifier {
  ReportsScreenController() {
    getReports();
  }

  List<ReportDocument> reports = [];
  bool isLoading = false;
  String? errorMessage;

  Future<void> getReports() async {
    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      // Get server key
      await di<ServerKeyController>().getServerKey();
      final serverKey = di<ServerKeyController>().serverKey;

      if (!di<ServerKeyController>().hasServerKey) {
        di<SnackBarShow>().showSnackBar("No server key available", Colors.red);
        return;
      }

      // Make API call
      final backendApi = di<IBackendApi>();
      final response = await backendApi.getReports(serverKey);

      reports = response.documents;

      debugPrint('Successfully loaded ${reports.length} reports');
    } catch (e) {
      errorMessage = e.toString();

      di<SnackBarShow>().showSnackBar(
        "Error : We couldn't load reports",
        Colors.red,
      );
      debugPrint('Error loading reports: $e');
      reports = [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}
