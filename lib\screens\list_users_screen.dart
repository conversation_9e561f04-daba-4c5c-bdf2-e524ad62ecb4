import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';

class ListUsersScreen extends StatelessWidget with WatchItMixin {
  const ListUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final users = controller.users;
    final isLoading = controller.isLoading;
    final errorMessage = controller.errorMessage;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('List of Users (${controller.totalUsers})'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.getUsers(),
          ),
        ],
      ),
      body: _buildBody(isLoading, errorMessage, users, controller),
    );
  }

  Widget _buildBody(
    bool isLoading,
    String? errorMessage,
    List<BackendUser> users,
    ListUserController controller,
  ) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: AppColours.error),
            const SizedBox(height: 16),
            Text(
              'Error loading users',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(color: AppColours.error),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.getUsers(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (users.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: AppColours.grey),
            SizedBox(height: 16),
            Text(
              'No users found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(BackendUser user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: user.isAdmin
              ? AppColours.success
              : AppColours.midBlue,
          child: Icon(
            user.isAdmin ? Icons.admin_panel_settings : Icons.person,
            color: AppColours.white,
          ),
        ),
        title: Text(
          user.name.isNotEmpty ? user.name : 'No Name',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.email),
            const SizedBox(height: 4),
            Row(
              children: [
                if (user.isAdmin) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColours.success,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Admin',
                      style: TextStyle(
                        color: AppColours.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: user.status ? AppColours.success : AppColours.error,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.status ? 'Active' : 'Inactive',
                    style: const TextStyle(
                      color: AppColours.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: user.serverKey != null
            ? const Icon(Icons.key, color: AppColours.success)
            : null,
        onTap: () => _showUserDetails(user),
      ),
    );
  }

  void _showUserDetails(BackendUser user) {
    // You can implement user details dialog here
    debugPrint('Show details for user: ${user.name}');
  }
}
