import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../theme/app_colours.dart';

class ListUsersScreen extends StatelessWidget with WatchItMixin {
  const ListUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final users = watchIt<ListUserController>().users;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('List of Users'),
        centerTitle: true,
      ),
    );
  }
}
