import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';

class ListUsersScreen extends StatelessWidget with WatchItMixin {
  const ListUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final users = controller.users;
    final isLoading = controller.isLoading;
    final errorMessage = controller.errorMessage;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('List of Users (${controller.totalUsers})'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => di<ListUserController>().getUsers(),
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView(
              children: users
                  .map(
                    (user) => Card(
                      child: ListTile(
                        title: Text(user.name),
                        subtitle: Text(user.email),
                        trailing: Icon(
                          user.isAdmin
                              ? Icons.admin_panel_settings
                              : Icons.person,
                        ),
                        onTap: () {
                          di<ListUserController>().getUserInfo(user.id);
                          context.push('/userDetailsScreen/${user.id}');
                        },
                      ),
                    ),
                  )
                  .toList(),
            ),
    );
  }
}
