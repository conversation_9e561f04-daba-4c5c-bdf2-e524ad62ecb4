import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/backend_user.dart';
import '../theme/app_colours.dart';
import '../theme/app_spacing.dart';
import '../theme/app_typography.dart';

class ListUsersScreen extends StatelessWidget with WatchItMixin {
  const ListUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final users = controller.users;
    final isLoading = controller.isLoading;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('List of Users (${controller.totalUsers})'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => di<ListUserController>().getUsers(),
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView(
              children: users
                  .map(
                    (user) => Dismissible(
                      key: Key(user.id),
                      direction: user.isAdmin
                          ? DismissDirection.none
                          : DismissDirection.endToStart,
                      confirmDismiss: (direction) async {
                        if (user.isAdmin) {
                          _showAdminProtectionDialog(context);
                          return false;
                        }
                        return await _showDeleteConfirmationDialog(
                          context,
                          user,
                        );
                      },
                      onDismissed: (direction) {
                        di<ListUserController>().deleteUser(user.id);
                      },
                      background: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(
                          right: AppSpacing.paddingLG,
                        ),
                        color: AppColours.error,
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.delete,
                              color: AppColours.white,
                              size: AppSpacing.iconLG,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Delete',
                              style: TextStyle(
                                color: AppColours.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      child: Card(
                        child: ListTile(
                          title: Text(user.name, style: AppTypography.h4),
                          subtitle: Text(user.email, style: AppTypography.h6),
                          trailing: Icon(
                            size: AppSpacing.iconLG,
                            color: user.isAdmin
                                ? AppColours.primaryBlue
                                : AppColours.midBlue,
                            user.isAdmin
                                ? Icons.admin_panel_settings
                                : Icons.person,
                          ),
                          onTap: () {
                            di<ListUserController>().getUserInfo(user.id);
                            context.push('/userDetailsScreen/${user.id}');
                          },
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
    );
  }

  void _showAdminProtectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.shield, color: AppColours.warning, size: 28),
              SizedBox(width: 12),
              Text('Admin Protected'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.admin_panel_settings,
                size: 64,
                color: AppColours.warning,
              ),
              SizedBox(height: 16),
              Text(
                'Admin users cannot be deleted!',
                style: AppTypography.h6,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'Admin users are protected from deletion to maintain system security.',
                style: AppTypography.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColours.primaryBlue,
                foregroundColor: AppColours.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<bool?> _showDeleteConfirmationDialog(
    BuildContext context,
    BackendUser user,
  ) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: AppColours.warning, size: 28),
              SizedBox(width: 12),
              Text('Confirm Delete'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Are you sure you want to delete this user?',
                style: AppTypography.bodyLarge,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColours.lightBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User: ${user.name}',
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Email: ${user.email}',
                      style: AppTypography.bodySmall,
                    ),
                    if (user.isAdmin) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColours.warning,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'ADMIN USER',
                          style: TextStyle(
                            color: AppColours.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This action cannot be undone.',
                style: TextStyle(
                  color: AppColours.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppColours.textSecondary),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColours.error,
                foregroundColor: AppColours.white,
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.delete, size: 18),
                  SizedBox(width: 8),
                  Text('Delete'),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
