import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';

class ListUsersScreen extends StatelessWidget with WatchItMixin {
  const ListUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final users = controller.users;
    final isLoading = controller.isLoading;
    final errorMessage = controller.errorMessage;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('List of Users (${controller.totalUsers})'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.getUsers(),
          ),
        ],
      ),
      body: _buildBody(isLoading, errorMessage, users, controller),
    );
  }

  Widget _buildBody(
    bool isLoading,
    String? errorMessage,
    List<BackendUser> users,
    ListUserController controller,
  ) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: AppColours.error),
            const SizedBox(height: 16),
            Text(
              'Error loading users',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(color: AppColours.error),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.getUsers(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (users.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: AppColours.grey),
            SizedBox(height: 16),
            Text(
              'No users found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserCard(context, user);
      },
    );
  }

  Widget _buildUserCard(BuildContext context, BackendUser user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: user.isAdmin
              ? AppColours.success
              : AppColours.midBlue,
          child: Icon(
            user.isAdmin ? Icons.admin_panel_settings : Icons.person,
            color: AppColours.white,
          ),
        ),
        title: Text(
          user.name.isNotEmpty ? user.name : 'No Name',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.email),
            const SizedBox(height: 4),
            Row(
              children: [
                if (user.isAdmin) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColours.success,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Admin',
                      style: TextStyle(
                        color: AppColours.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: user.status ? AppColours.success : AppColours.error,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.status ? 'Active' : 'Inactive',
                    style: const TextStyle(
                      color: AppColours.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: user.serverKey != null
            ? const Icon(Icons.key, color: AppColours.success)
            : null,
        onTap: () => _showUserDetails(context, user),
      ),
    );
  }

  void _showUserDetails(BuildContext context, BackendUser user) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Get user info from API
      final controller = di<ListUserController>();
      final userInfo = await controller.getUserInfo(user.id);

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (userInfo != null && context.mounted) {
        _showUserInfoDialog(context, user, userInfo);
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load user details'),
            backgroundColor: AppColours.error,
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColours.error,
          ),
        );
      }
    }
  }

  void _showUserInfoDialog(
    BuildContext context,
    BackendUser user,
    UserInfoResponse userInfo,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('User Details: ${user.name}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Email', user.email),
              _buildDetailRow('User ID', user.id),
              _buildDetailRow('Status', user.status ? 'Active' : 'Inactive'),
              _buildDetailRow('Admin', user.isAdmin ? 'Yes' : 'No'),
              _buildDetailRow('Created', user.createdAt),
              _buildDetailRow('Last Access', user.accessedAt),
              if (user.serverKey != null)
                _buildDetailRow('Has Server Key', 'Yes'),

              const SizedBox(height: 16),
              const Text(
                'Profile Information:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),

              _buildDetailRow('Profile ID', userInfo.profile.id),
              _buildDetailRow('Collection ID', userInfo.profile.collectionId),
              _buildDetailRow('Database ID', userInfo.profile.databaseId),

              // Show additional profile attributes
              if (userInfo.profile.additionalAttributes.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text(
                  'Additional Attributes:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                ...userInfo.profile.additionalAttributes.entries.map(
                  (entry) => _buildDetailRow(entry.key, entry.value.toString()),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value.isNotEmpty ? value : 'N/A',
              style: const TextStyle(color: AppColours.textSecondary),
            ),
          ),
        ],
      ),
    );
  }
}
