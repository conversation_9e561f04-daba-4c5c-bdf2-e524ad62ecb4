import 'backend_user.dart';

class UsersData {
  final int total;
  final List<BackendUser> users;

  UsersData({
    required this.total,
    required this.users,
  });

  factory UsersData.fromJson(Map<String, dynamic> json) {
    return UsersData(
      total: json['total'] ?? 0,
      users: (json['users'] as List<dynamic>?)
          ?.map((user) => BackendUser.fromJson(user))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'users': users.map((user) => user.toJson()).toList(),
    };
  }
}

class UsersResponse {
  final UsersData data;
  final String message;

  UsersResponse({
    required this.data,
    required this.message,
  });

  factory UsersResponse.fromJson(Map<String, dynamic> json) {
    return UsersResponse(
      data: UsersData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
      'message': message,
    };
  }

  // Helper getters
  List<BackendUser> get users => data.users;
  int get totalUsers => data.total;
  List<BackendUser> get adminUsers => users.where((user) => user.isAdmin).toList();
  List<BackendUser> get regularUsers => users.where((user) => !user.isAdmin).toList();
}
