import 'hash_options.dart';
import 'user_target.dart';

class BackendUser {
  final String id;
  final String createdAt;
  final String updatedAt;
  final String accessedAt;
  final String email;
  final bool emailVerification;
  final String hash;
  final HashOptions hashOptions;
  final List<String> labels;
  final bool mfa;
  final String name;
  final String password;
  final String passwordUpdate;
  final String phone;
  final bool phoneVerification;
  final Map<String, dynamic> prefs;
  final String registration;
  final bool status;
  final List<UserTarget> targets;

  BackendUser({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.accessedAt,
    required this.email,
    required this.emailVerification,
    required this.hash,
    required this.hashOptions,
    required this.labels,
    required this.mfa,
    required this.name,
    required this.password,
    required this.passwordUpdate,
    required this.phone,
    required this.phoneVerification,
    required this.prefs,
    required this.registration,
    required this.status,
    required this.targets,
  });

  factory BackendUser.fromJson(Map<String, dynamic> json) {
    return BackendUser(
      id: json['\$id'] ?? '',
      createdAt: json['\$createdAt'] ?? '',
      updatedAt: json['\$updatedAt'] ?? '',
      accessedAt: json['accessedAt'] ?? '',
      email: json['email'] ?? '',
      emailVerification: json['emailVerification'] ?? false,
      hash: json['hash'] ?? '',
      hashOptions: HashOptions.fromJson(json['hashOptions'] ?? {}),
      labels: List<String>.from(json['labels'] ?? []),
      mfa: json['mfa'] ?? false,
      name: json['name'] ?? '',
      password: json['password'] ?? '',
      passwordUpdate: json['passwordUpdate'] ?? '',
      phone: json['phone'] ?? '',
      phoneVerification: json['phoneVerification'] ?? false,
      prefs: Map<String, dynamic>.from(json['prefs'] ?? {}),
      registration: json['registration'] ?? '',
      status: json['status'] ?? false,
      targets: (json['targets'] as List<dynamic>?)
          ?.map((target) => UserTarget.fromJson(target))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      'accessedAt': accessedAt,
      'email': email,
      'emailVerification': emailVerification,
      'hash': hash,
      'hashOptions': hashOptions.toJson(),
      'labels': labels,
      'mfa': mfa,
      'name': name,
      'password': password,
      'passwordUpdate': passwordUpdate,
      'phone': phone,
      'phoneVerification': phoneVerification,
      'prefs': prefs,
      'registration': registration,
      'status': status,
      'targets': targets.map((target) => target.toJson()).toList(),
    };
  }

  // Helper getters
  bool get isAdmin => labels.contains('admin');
  String? get serverKey => prefs['key'];
}
