import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';

import '../exceptions/repository_exception.dart';
import 'i_auth_repository.dart';

class AuthRepository implements IAuthRepository {
  final Account _account;

  AuthRepository({required Account account}) : _account = account;

  @override
  Future<Session> login(String email, String password) async {
    try {
      final response = await _account.createEmailPasswordSession(
        email: email,
        password: password,
      );

      return response;
    } on AppwriteException catch (e) {
      throw RepositoryException('Failed to login : ${e.message}');
    }
  }

  @override
  Future<User> getCurrentUser() async {
    try {
      final response = await _account.get();
      return response;
    } on AppwriteException catch (e) {
      throw RepositoryException('Failed to get current user: ${e.message}');
    }
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      await _account.get();
      return true;
    } on AppwriteException catch (_) {
      return false;
    }
  }

  @override
  Future<void> logout() async {
    try {
      await _account.deleteSession(sessionId: 'current');
    } on AppwriteException catch (e) {
      throw RepositoryException('Failed to logout: ${e.message}');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final response = await _account.getPrefs();
      return response.data;
    } on AppwriteException catch (e) {
      throw RepositoryException('Failed to get user preferences: ${e.message}');
    }
  }
}
