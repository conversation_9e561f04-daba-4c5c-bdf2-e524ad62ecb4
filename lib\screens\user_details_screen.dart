import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';
import '../theme/app_typography.dart';

class UserDetailsScreen extends StatelessWidget with WatchItMixin {
  final String userId;

  const UserDetailsScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final userInfo = controller.userInfo;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: const Text('User Details'),
        centerTitle: true,
      ),
      body: controller.isLoadingUserInfo
          ? const Center(child: CircularProgressIndicator())
          : userInfo == null
          ? const Center(
              child: Text(
                'The user as not added profile yet.',
                style: AppTypography.h5,
              ),
            )
          : _buildUserDetails(userInfo),
    );
  }

  Widget _buildUserDetails(UserInfoResponse userInfo) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Contact Information',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              _buildDetailRow('Address 1', userInfo.profile.address1),
              _buildDetailRow('Address 2', userInfo.profile.address2),
              _buildDetailRow('Post Code', userInfo.profile.postCode),
              _buildDetailRow('Telephone', userInfo.profile.telephone),
              _buildDetailRow('Consultant', userInfo.profile.consultantName),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 85,
            child: Text('$label:', style: AppTypography.bodyMedium),
          ),
          Expanded(
            child: Text(
              value.isNotEmpty ? value : 'N/A',
              style: AppTypography.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
