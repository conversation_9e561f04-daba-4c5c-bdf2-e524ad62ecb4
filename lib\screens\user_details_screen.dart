import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../models/models.dart';
import '../theme/app_colours.dart';

class UserDetailsScreen extends StatelessWidget with WatchItMixin {
  final String userId;

  const UserDetailsScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();
    final userInfo = controller.userInfo;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: const Text('User Details'),
        centerTitle: true,
      ),
      body: controller.isLoadingUserInfo
          ? const Center(child: CircularProgressIndicator())
          : userInfo == null
          ? const Center(child: Text('No user information available'))
          : _buildUserDetails(userInfo),
    );
  }

  Widget _buildUserDetails(UserInfoResponse userInfo) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Basic Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow('Message', userInfo.message),
                  _buildDetailRow('User ID', userInfo.userId),
                  _buildDetailRow('Profile ID', userInfo.profile.id),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Contact Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow('Address 1', userInfo.profile.address1),
                  _buildDetailRow('Address 2', userInfo.profile.address2),
                  _buildDetailRow('Post Code', userInfo.profile.postCode),
                  _buildDetailRow('Telephone', userInfo.profile.telephone),
                  _buildDetailRow(
                    'Consultant Name',
                    userInfo.profile.consultantName,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'System Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow(
                    'Collection ID',
                    userInfo.profile.collectionId,
                  ),
                  _buildDetailRow('Database ID', userInfo.profile.databaseId),
                  _buildDetailRow('Sequence', userInfo.profile.sequence),
                  _buildDetailRow('Created At', userInfo.profile.createdAt),
                  _buildDetailRow('Updated At', userInfo.profile.updatedAt),
                ],
              ),
            ),
          ),

          if (userInfo.profile.permissions.isNotEmpty) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Permissions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...userInfo.profile.permissions.map(
                      (permission) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          '• $permission',
                          style: const TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          if (userInfo.profile.additionalAttributes.isNotEmpty) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Attributes',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...userInfo.profile.additionalAttributes.entries.map(
                      (entry) =>
                          _buildDetailRow(entry.key, entry.value.toString()),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value.isNotEmpty ? value : 'N/A',
              style: const TextStyle(color: AppColours.textSecondary),
            ),
          ),
        ],
      ),
    );
  }
}
