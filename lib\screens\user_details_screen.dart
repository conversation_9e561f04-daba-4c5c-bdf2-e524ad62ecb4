import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/list_user_controller.dart';
import '../theme/app_colours.dart';

class UserDetailsScreen extends StatelessWidget with WatchItMixin {
  const UserDetailsScreen({super.key, required String userId});

  @override
  Widget build(BuildContext context) {
    final controller = watchIt<ListUserController>();

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColours.lightBlue,
        title: Text('User Details'),
        centerTitle: true,
      ),
      body: controller.isLoadingUserInfo
          ? Center(child: CircularProgressIndicator())
          : ListView(children: [Text('${controller.userInfo}')]),
    );
  }
}
